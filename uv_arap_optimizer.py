import igl
import numpy as np
import argparse
import os
from scipy.sparse import csc_matrix
import time

class UVARAPOptimizer:
    """使用libigl的ARAP方法优化UV映射"""
    
    def __init__(self):
        self.vertices = None
        self.faces = None
        self.uv_coords = None
        self.boundary_vertices = None
        
    def load_obj(self, filepath):
        """加载OBJ文件"""
        print(f"Loading OBJ file: {filepath}")
        
        # 使用igl加载OBJ文件
        self.vertices, self.faces = igl.read_triangle_mesh(filepath)
        
        # 尝试读取UV坐标
        try:
            # 读取OBJ文件中的纹理坐标
            vertices_list = []
            faces_list = []
            uv_list = []
            face_uv_indices = []
            
            with open(filepath, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('v '):
                        # 顶点坐标
                        parts = line.split()
                        vertices_list.append([float(parts[1]), float(parts[2]), float(parts[3])])
                    elif line.startswith('vt '):
                        # 纹理坐标
                        parts = line.split()
                        uv_list.append([float(parts[1]), float(parts[2])])
                    elif line.startswith('f '):
                        # 面信息
                        parts = line.split()
                        face_vertices = []
                        face_uvs = []
                        for vertex_data in parts[1:]:
                            indices = vertex_data.split('/')
                            face_vertices.append(int(indices[0]) - 1)  # 顶点索引
                            if len(indices) > 1 and indices[1]:
                                face_uvs.append(int(indices[1]) - 1)  # UV索引
                        
                        if len(face_vertices) == 3:
                            faces_list.append(face_vertices)
                            if len(face_uvs) == 3:
                                face_uv_indices.append(face_uvs)
            
            if uv_list and face_uv_indices:
                # 重新组织UV坐标以匹配顶点顺序
                self.uv_coords = np.zeros((len(vertices_list), 2))
                for face_idx, (face, uv_face) in enumerate(zip(faces_list, face_uv_indices)):
                    for i in range(3):
                        vertex_idx = face[i]
                        uv_idx = uv_face[i]
                        if uv_idx < len(uv_list):
                            self.uv_coords[vertex_idx] = uv_list[uv_idx]
                
                print(f"Loaded {len(uv_list)} UV coordinates")
            else:
                print("No UV coordinates found, will generate initial parameterization")
                self.uv_coords = None
                
        except Exception as e:
            print(f"Error reading UV coordinates: {e}")
            self.uv_coords = None
        
        print(f"Loaded {len(self.vertices)} vertices and {len(self.faces)} faces")
        return True
    
    def find_boundary_vertices(self):
        """找到网格边界顶点"""
        print("Finding boundary vertices...")
        boundary_loop = igl.boundary_loop(self.faces)
        self.boundary_vertices = boundary_loop
        print(f"Found {len(self.boundary_vertices)} boundary vertices")
        return self.boundary_vertices
    
    def generate_initial_parameterization(self):
        """生成初始UV参数化"""
        if self.uv_coords is not None:
            print("Using existing UV coordinates as initial parameterization")
            return self.uv_coords
        
        print("Generating initial UV parameterization using harmonic mapping...")
        
        # 找到边界顶点
        if self.boundary_vertices is None:
            self.find_boundary_vertices()
        
        # 将边界顶点映射到单位圆
        boundary_uv = np.zeros((len(self.boundary_vertices), 2))
        for i, vertex_idx in enumerate(self.boundary_vertices):
            angle = 2 * np.pi * i / len(self.boundary_vertices)
            boundary_uv[i] = [0.5 + 0.4 * np.cos(angle), 0.5 + 0.4 * np.sin(angle)]
        
        # 使用调和映射计算内部顶点的UV坐标
        uv_coords = igl.harmonic_weights(self.vertices, self.faces, self.boundary_vertices, boundary_uv, 1)
        
        self.uv_coords = uv_coords
        print("Initial parameterization generated")
        return uv_coords
    
    def optimize_with_arap(self, max_iterations=50, energy_threshold=1e-6):
        """使用ARAP方法优化UV映射"""
        print("Starting ARAP optimization...")

        # 确保有初始UV坐标
        if self.uv_coords is None:
            self.generate_initial_parameterization()

        # 找到边界顶点（如果还没有找到）
        if self.boundary_vertices is None:
            self.find_boundary_vertices()

        # 使用更稳定的ARAP实现
        print("Setting up ARAP optimization...")

        # 固定边界顶点
        if len(self.boundary_vertices) > 0:
            fixed_vertices = np.array(self.boundary_vertices, dtype=np.int32)
            fixed_positions = self.uv_coords[fixed_vertices].copy()
        else:
            # 如果没有边界，固定几个顶点防止平移
            fixed_vertices = np.array([0, len(self.vertices)//2], dtype=np.int32)
            fixed_positions = self.uv_coords[fixed_vertices].copy()

        print(f"Fixed {len(fixed_vertices)} vertices")

        try:
            # 使用igl的ARAP求解器
            # 注意：这里使用2D ARAP，因为我们在优化UV坐标
            current_uv = self.uv_coords.copy()

            # 构建拉普拉斯矩阵用于ARAP
            L = igl.cotmatrix(self.vertices, self.faces)

            # 迭代ARAP优化
            for iteration in range(max_iterations):
                print(f"ARAP iteration {iteration + 1}/{max_iterations}")

                # 局部步骤：计算旋转
                rotations = self.compute_local_rotations_2d(current_uv)

                # 全局步骤：求解线性系统
                new_uv = self.solve_global_arap(current_uv, rotations, L, fixed_vertices, fixed_positions)

                # 计算能量变化
                energy = self.compute_arap_energy(current_uv, new_uv)
                if iteration > 0:
                    energy_change = abs(prev_energy - energy)
                    print(f"  Energy: {energy:.6f}, Change: {energy_change:.6f}")

                    # 检查收敛
                    if energy_change < energy_threshold:
                        print(f"Converged after {iteration + 1} iterations")
                        break
                else:
                    print(f"  Energy: {energy:.6f}")

                current_uv = new_uv
                prev_energy = energy

        except Exception as e:
            print(f"ARAP optimization failed: {e}")
            print("Falling back to simple smoothing...")
            current_uv = self.smooth_uv_coordinates(self.uv_coords, iterations=max_iterations//5)

        self.uv_coords = current_uv
        print("ARAP optimization completed")
        return current_uv
    
    def compute_local_rotations_2d(self, uv):
        """计算每个三角形的局部旋转"""
        rotations = []

        for face in self.faces:
            v0, v1, v2 = face

            # 3D三角形的边
            e1_3d = self.vertices[v1] - self.vertices[v0]
            e2_3d = self.vertices[v2] - self.vertices[v0]

            # UV三角形的边
            e1_uv = uv[v1] - uv[v0]
            e2_uv = uv[v2] - uv[v0]

            # 计算最优旋转矩阵（2D）
            try:
                # 构建协方差矩阵
                P = np.column_stack([e1_3d[:2], e2_3d[:2]])  # 3D边（只取前两个分量）
                Q = np.column_stack([e1_uv, e2_uv])         # UV边

                if np.linalg.det(P) > 1e-8 and np.linalg.det(Q) > 1e-8:
                    # 计算旋转矩阵 R = Q * P^(-1)
                    R = Q @ np.linalg.inv(P)

                    # 提取旋转部分（极分解）
                    U, _, Vt = np.linalg.svd(R)
                    R_rot = U @ Vt

                    # 确保是旋转矩阵（行列式为1）
                    if np.linalg.det(R_rot) < 0:
                        U[:, -1] *= -1
                        R_rot = U @ Vt
                else:
                    R_rot = np.eye(2)

            except:
                R_rot = np.eye(2)

            rotations.append(R_rot)

        return rotations

    def solve_global_arap(self, current_uv, rotations, L, fixed_vertices, fixed_positions):
        """求解ARAP的全局步骤"""
        n = len(self.vertices)

        # 构建右端项
        b = np.zeros((n, 2))

        # 添加旋转约束
        for i, face in enumerate(self.faces):
            v0, v1, v2 = face
            R = rotations[i]

            # 添加旋转约束到右端项
            weight = 1.0
            b[v0] += weight * R @ (self.vertices[v1][:2] - self.vertices[v0][:2])
            b[v1] -= weight * R @ (self.vertices[v1][:2] - self.vertices[v0][:2])

        # 处理固定约束
        A = L.toarray()

        # 修改系统以处理固定顶点
        for i, vertex_idx in enumerate(fixed_vertices):
            # 清除该行
            A[vertex_idx, :] = 0
            A[vertex_idx, vertex_idx] = 1
            b[vertex_idx] = fixed_positions[i]

        # 添加正则化
        A += 1e-6 * np.eye(n)

        # 求解线性系统
        try:
            new_uv = np.zeros((n, 2))
            new_uv[:, 0] = np.linalg.solve(A, b[:, 0])
            new_uv[:, 1] = np.linalg.solve(A, b[:, 1])
        except:
            # 如果求解失败，返回当前UV
            new_uv = current_uv.copy()

        return new_uv

    def smooth_uv_coordinates(self, uv, iterations=10):
        """简单的UV坐标平滑（备用方案）"""
        print(f"Applying UV smoothing for {iterations} iterations...")

        current_uv = uv.copy()

        for iteration in range(iterations):
            new_uv = current_uv.copy()

            for face in self.faces:
                v0, v1, v2 = face

                # 简单的拉普拉斯平滑
                center = (current_uv[v0] + current_uv[v1] + current_uv[v2]) / 3.0

                # 向中心移动一小步
                alpha = 0.1
                new_uv[v0] = (1 - alpha) * current_uv[v0] + alpha * center
                new_uv[v1] = (1 - alpha) * current_uv[v1] + alpha * center
                new_uv[v2] = (1 - alpha) * current_uv[v2] + alpha * center

            # 保持边界顶点固定
            if self.boundary_vertices is not None:
                new_uv[self.boundary_vertices] = uv[self.boundary_vertices]

            current_uv = new_uv

        return current_uv

    def compute_arap_energy(self, uv_old, uv_new):
        """计算ARAP能量"""
        energy = 0.0

        for face in self.faces:
            v0, v1, v2 = face

            # 3D三角形的边
            e1_3d = self.vertices[v1] - self.vertices[v0]
            e2_3d = self.vertices[v2] - self.vertices[v0]

            # UV三角形的边
            e1_uv = uv_new[v1] - uv_new[v0]
            e2_uv = uv_new[v2] - uv_new[v0]

            # 计算变形梯度
            try:
                P = np.column_stack([e1_3d[:2], e2_3d[:2]])
                Q = np.column_stack([e1_uv, e2_uv])

                if np.linalg.det(P) > 1e-8:
                    F = Q @ np.linalg.inv(P)

                    # ARAP能量：||F - R||^2，其中R是最优旋转
                    U, s, Vt = np.linalg.svd(F)
                    R = U @ Vt
                    if np.linalg.det(R) < 0:
                        U[:, -1] *= -1
                        R = U @ Vt

                    energy += np.linalg.norm(F - R, 'fro') ** 2

            except:
                pass

        return energy
    
    def save_obj(self, output_path):
        """保存优化后的OBJ文件"""
        print(f"Saving optimized mesh to: {output_path}")
        
        with open(output_path, 'w') as f:
            # 写入顶点
            for vertex in self.vertices:
                f.write(f"v {vertex[0]:.6f} {vertex[1]:.6f} {vertex[2]:.6f}\n")
            
            # 写入UV坐标
            for uv in self.uv_coords:
                f.write(f"vt {uv[0]:.6f} {uv[1]:.6f}\n")
            
            # 写入面（假设每个顶点都有对应的UV坐标）
            for face in self.faces:
                f.write(f"f {face[0]+1}/{face[0]+1} {face[1]+1}/{face[1]+1} {face[2]+1}/{face[2]+1}\n")
        
        print("Mesh saved successfully")
    
    def compute_distortion_metrics(self):
        """计算UV映射的失真度量"""
        print("Computing distortion metrics...")
        
        # 计算角度失真
        angle_distortion = self.compute_angle_distortion()
        
        # 计算面积失真
        area_distortion = self.compute_area_distortion()
        
        print(f"Average angle distortion: {np.mean(angle_distortion):.4f}")
        print(f"Max angle distortion: {np.max(angle_distortion):.4f}")
        print(f"Average area distortion: {np.mean(area_distortion):.4f}")
        print(f"Max area distortion: {np.max(area_distortion):.4f}")
        
        return angle_distortion, area_distortion
    
    def compute_angle_distortion(self):
        """计算角度失真"""
        # 简化的角度失真计算
        distortions = []
        for face in self.faces:
            # 3D空间中的角度
            v0, v1, v2 = self.vertices[face]
            e1_3d = v1 - v0
            e2_3d = v2 - v0
            angle_3d = np.arccos(np.clip(np.dot(e1_3d, e2_3d) / (np.linalg.norm(e1_3d) * np.linalg.norm(e2_3d)), -1, 1))
            
            # UV空间中的角度
            uv0, uv1, uv2 = self.uv_coords[face]
            e1_uv = uv1 - uv0
            e2_uv = uv2 - uv0
            angle_uv = np.arccos(np.clip(np.dot(e1_uv, e2_uv) / (np.linalg.norm(e1_uv) * np.linalg.norm(e2_uv)), -1, 1))
            
            distortion = abs(angle_3d - angle_uv)
            distortions.append(distortion)
        
        return np.array(distortions)
    
    def compute_area_distortion(self):
        """计算面积失真"""
        distortions = []
        for face in self.faces:
            # 3D空间中的面积
            v0, v1, v2 = self.vertices[face]
            area_3d = 0.5 * np.linalg.norm(np.cross(v1 - v0, v2 - v0))
            
            # UV空间中的面积
            uv0, uv1, uv2 = self.uv_coords[face]
            # 2D叉积的模长
            area_uv = 0.5 * abs((uv1[0] - uv0[0]) * (uv2[1] - uv0[1]) - (uv2[0] - uv0[0]) * (uv1[1] - uv0[1]))
            
            if area_3d > 0 and area_uv > 0:
                distortion = max(area_uv / area_3d, area_3d / area_uv)
            else:
                distortion = 1.0
            
            distortions.append(distortion)
        
        return np.array(distortions)

def main():
    parser = argparse.ArgumentParser(description="Optimize UV mapping using libigl ARAP method")
    parser.add_argument("input", help="Input OBJ file path")
    parser.add_argument("output", help="Output OBJ file path")
    parser.add_argument("--max-iterations", type=int, default=50, help="Maximum ARAP iterations")
    parser.add_argument("--energy-threshold", type=float, default=1e-6, help="Energy convergence threshold")
    parser.add_argument("--compute-metrics", action="store_true", help="Compute and display distortion metrics")
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"Error: Input file {args.input} does not exist")
        return
    
    # 确保输出目录存在
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    try:
        # 创建优化器
        optimizer = UVARAPOptimizer()
        
        # 加载模型
        start_time = time.time()
        optimizer.load_obj(args.input)
        
        # 执行ARAP优化
        optimizer.optimize_with_arap(
            max_iterations=args.max_iterations,
            energy_threshold=args.energy_threshold
        )
        
        # 保存结果
        optimizer.save_obj(args.output)
        
        # 计算失真度量（如果请求）
        if args.compute_metrics:
            optimizer.compute_distortion_metrics()
        
        total_time = time.time() - start_time
        print(f"Total processing time: {total_time:.2f} seconds")
        print("UV optimization completed successfully!")
        
    except Exception as e:
        print(f"Error during UV optimization: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
