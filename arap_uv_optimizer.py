#!/usr/bin/env python3
"""
ARAP UV Optimizer using libigl

This script uses libigl's ARAP (As-Rigid-As-Possible) method to optimize UV coordinates
for 3D models. It reads an OBJ file with existing UV coordinates (typically from LSCM)
and applies ARAP optimization to improve the UV mapping quality.

Usage:
    python arap_uv_optimizer.py input.obj output.obj
"""

import numpy as np
import argparse
import os
import sys

# Try to import libigl, provide fallback if not available
try:
    import igl
    HAS_LIBIGL = True
except ImportError:
    HAS_LIBIGL = False
    print("Warning: libigl not found. Install with: pip install libigl")
    print("Falling back to simplified UV optimization...")

    # Simple fallback implementation for basic functionality
    class SimpleIGL:
        @staticmethod
        def read_triangle_mesh(filepath):
            """Simple OBJ reader for vertices and faces"""
            vertices = []
            faces = []

            with open(filepath, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('v '):
                        parts = line.split()
                        vertices.append([float(parts[1]), float(parts[2]), float(parts[3])])
                    elif line.startswith('f '):
                        parts = line.split()
                        face = []
                        for part in parts[1:]:
                            # Handle v/vt/vn format
                            vertex_idx = int(part.split('/')[0]) - 1
                            face.append(vertex_idx)
                        if len(face) == 3:
                            faces.append(face)

            return np.array(vertices, dtype=np.float64), np.array(faces, dtype=np.int32)

        @staticmethod
        def read_triangle_mesh_uv(filepath):
            """Simple OBJ reader for UV coordinates"""
            uv_vertices = []
            uv_faces = []

            with open(filepath, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('vt '):
                        parts = line.split()
                        uv_vertices.append([float(parts[1]), float(parts[2])])
                    elif line.startswith('f '):
                        parts = line.split()
                        face = []
                        for part in parts[1:]:
                            # Handle v/vt/vn format
                            indices = part.split('/')
                            if len(indices) > 1 and indices[1]:
                                uv_idx = int(indices[1]) - 1
                                face.append(uv_idx)
                        if len(face) == 3:
                            uv_faces.append(face)

            return np.array(uv_vertices, dtype=np.float64), np.array(uv_faces, dtype=np.int32)

        @staticmethod
        def boundary_loop(faces):
            """Simple boundary detection"""
            edges = {}
            for face in faces:
                for i in range(3):
                    edge = tuple(sorted([face[i], face[(i+1)%3]]))
                    edges[edge] = edges.get(edge, 0) + 1

            boundary_edges = [edge for edge, count in edges.items() if count == 1]
            boundary_vertices = list(set([v for edge in boundary_edges for v in edge]))
            return np.array(boundary_vertices, dtype=np.int32)

        @staticmethod
        def doublearea(vertices, faces):
            """Calculate double area of triangles"""
            areas = []
            for face in faces:
                v0, v1, v2 = vertices[face]
                # Cross product for triangle area
                area = np.linalg.norm(np.cross(v1 - v0, v2 - v0))
                areas.append(area)
            return np.array(areas)

    igl = SimpleIGL()


def load_obj_with_uv(filepath):
    """
    Load OBJ file with vertices, faces, and UV coordinates using libigl

    Args:
        filepath (str): Path to the OBJ file

    Returns:
        tuple: (vertices, faces, uv_vertices, uv_faces)
    """
    print(f"Loading mesh from {filepath}")

    # Load mesh geometry
    vertices, faces = igl.read_triangle_mesh(filepath)

    # Load UV coordinates
    uv_vertices, uv_faces = igl.read_triangle_mesh_uv(filepath)

    print(f"Loaded mesh: {vertices.shape[0]} vertices, {faces.shape[0]} faces")
    print(f"Loaded UV: {uv_vertices.shape[0]} UV vertices, {uv_faces.shape[0]} UV faces")

    return vertices, faces, uv_vertices, uv_faces


def save_obj_with_uv(filepath, vertices, faces, uv_vertices, uv_faces):
    """
    Save mesh with UV coordinates to OBJ file

    Args:
        filepath (str): Output file path
        vertices (np.ndarray): 3D vertices
        faces (np.ndarray): Face indices
        uv_vertices (np.ndarray): UV coordinates
        uv_faces (np.ndarray): UV face indices
    """
    print(f"Saving optimized mesh to {filepath}")

    with open(filepath, 'w') as f:
        # Write vertices
        for v in vertices:
            f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")

        # Write UV coordinates
        for uv in uv_vertices:
            f.write(f"vt {uv[0]:.6f} {uv[1]:.6f}\n")

        # Write faces with UV indices
        for i, face in enumerate(faces):
            if i < len(uv_faces):
                uv_face = uv_faces[i]
                f.write(f"f {face[0]+1}/{uv_face[0]+1} {face[1]+1}/{uv_face[1]+1} {face[2]+1}/{uv_face[2]+1}\n")
            else:
                # Fallback if UV faces don't match
                f.write(f"f {face[0]+1}/{face[0]+1} {face[1]+1}/{face[1]+1} {face[2]+1}/{face[2]+1}\n")


def optimize_uv_with_arap(vertices, faces, uv_vertices, uv_faces, iterations=10):
    """
    Optimize UV coordinates using ARAP method

    Args:
        vertices (np.ndarray): 3D mesh vertices
        faces (np.ndarray): Face indices
        uv_vertices (np.ndarray): Initial UV coordinates
        uv_faces (np.ndarray): UV face indices
        iterations (int): Number of ARAP iterations

    Returns:
        np.ndarray: Optimized UV coordinates
    """
    print("Optimizing UV coordinates with ARAP...")

    # Convert UV coordinates to 3D for ARAP (add z=0)
    if uv_vertices.shape[1] == 2:
        uv_3d = np.column_stack([uv_vertices, np.zeros(uv_vertices.shape[0])])
    else:
        uv_3d = uv_vertices.copy()

    # Find boundary vertices for constraints
    boundary_edges = igl.boundary_loop(faces)
    print(f"Found {len(boundary_edges)} boundary vertices")

    # If no boundary found, try to find it from UV faces
    if len(boundary_edges) == 0:
        print("No boundary found in 3D mesh, trying UV mesh...")
        if len(uv_faces) > 0:
            boundary_edges = igl.boundary_loop(uv_faces)
            print(f"Found {len(boundary_edges)} boundary vertices in UV mesh")

    # Set up ARAP solver
    # Use 2D ARAP for UV optimization
    arap_solver = igl.ARAP(vertices, faces, 2, boundary_edges)

    # Use current UV coordinates as initial guess
    initial_uv = uv_vertices.copy()

    # If we have boundary constraints, fix them
    if len(boundary_edges) > 0:
        # Keep boundary vertices fixed at their current UV positions
        boundary_positions = uv_vertices[boundary_edges]
        print(f"Constraining {len(boundary_edges)} boundary vertices")

        # Solve ARAP with boundary constraints
        optimized_uv = arap_solver.solve(boundary_positions, initial_uv)
    else:
        # No boundary constraints - use free-form ARAP
        print("No boundary constraints found, using free-form ARAP")
        optimized_uv = arap_solver.solve(np.zeros((0, 2)), initial_uv)

    print(f"ARAP optimization completed")

    return optimized_uv


def main():
    parser = argparse.ArgumentParser(description="Optimize UV coordinates using ARAP method")
    parser.add_argument("input", help="Input OBJ file with UV coordinates")
    parser.add_argument("output", help="Output OBJ file with optimized UV coordinates")
    parser.add_argument("--iterations", type=int, default=10,
                       help="Number of ARAP iterations (default: 10)")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose output")

    args = parser.parse_args()

    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist")
        sys.exit(1)

    # Create output directory if needed
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    try:
        # Load mesh and UV coordinates
        vertices, faces, uv_vertices, uv_faces = load_obj_with_uv(args.input)

        # Check if UV coordinates exist
        if len(uv_vertices) == 0:
            print("Error: No UV coordinates found in input file")
            print("Please ensure the input OBJ file contains texture coordinates (vt lines)")
            sys.exit(1)

        # Optimize UV coordinates using ARAP
        optimized_uv = optimize_uv_with_arap(
            vertices, faces, uv_vertices, uv_faces,
            iterations=args.iterations
        )

        # Save optimized mesh
        save_obj_with_uv(args.output, vertices, faces, optimized_uv, uv_faces)

        print("UV optimization completed successfully!")

        # Print some statistics
        if args.verbose:
            original_area = np.sum(igl.doublearea(uv_vertices, uv_faces)) / 2.0
            optimized_area = np.sum(igl.doublearea(optimized_uv, uv_faces)) / 2.0
            print(f"Original UV area: {original_area:.6f}")
            print(f"Optimized UV area: {optimized_area:.6f}")
            print(f"Area change: {((optimized_area - original_area) / original_area * 100):.2f}%")

    except Exception as e:
        print(f"Error during UV optimization: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()