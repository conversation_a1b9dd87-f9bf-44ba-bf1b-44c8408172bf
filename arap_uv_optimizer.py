#!/usr/bin/env python3
"""
ARAP UV Optimization Script using libigl

This script uses libigl's ARAP (As-Rigid-As-Possible) method to optimize UV coordinates
for 3D models. It follows the official libigl documentation example.

Usage:
    python arap_uv_optimizer.py input.obj output.obj [--iterations N]

Dependencies:
    - libigl
    - numpy
    - argparse
    - os
"""

import os
import sys
import argparse
import numpy as np

try:
    import igl
except ImportError:
    print("Error: libigl is not installed. Please install it using:")
    print("pip install libigl")
    sys.exit(1)


class ARAPUVOptimizer:
    """ARAP UV optimization using libigl"""
    
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.vertices = None
        self.faces = None
        self.uv_coords = None
        
    def log(self, message):
        """Print message if verbose mode is enabled"""
        if self.verbose:
            print(message)
    
    def load_mesh(self, input_path):
        """Load mesh from OBJ file"""
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Input file not found: {input_path}")
        
        self.log(f"Loading mesh from: {input_path}")
        
        # Load triangle mesh using libigl
        self.vertices, self.faces = igl.read_triangle_mesh(input_path)
        
        if len(self.vertices) == 0 or len(self.faces) == 0:
            raise ValueError(f"Failed to load valid mesh from {input_path}")
        
        self.log(f"Loaded mesh with {len(self.vertices)} vertices and {len(self.faces)} faces")
        return True
    
    def compute_initial_uv(self):
        """Compute initial UV coordinates using harmonic parametrization"""
        self.log("Computing initial UV parametrization...")
        
        # Find the open boundary
        bnd = igl.boundary_loop(self.faces)
        
        if len(bnd) == 0:
            raise ValueError("No boundary found. ARAP UV optimization requires a mesh with boundary.")
        
        self.log(f"Found boundary with {len(bnd)} vertices")
        
        # Map the boundary to a circle, preserving edge proportions
        bnd_uv = igl.map_vertices_to_circle(self.vertices, bnd)
        
        # Harmonic parametrization for the internal vertices
        self.uv_coords = igl.harmonic(self.vertices, self.faces, bnd, bnd_uv, 1)
        
        self.log("Initial harmonic parametrization completed")
        return self.uv_coords
    
    def optimize_with_arap(self, max_iterations=10):
        """Optimize UV coordinates using ARAP method"""
        if self.uv_coords is None:
            raise ValueError("Initial UV coordinates not computed. Call compute_initial_uv() first.")
        
        self.log(f"Starting ARAP optimization with {max_iterations} iterations...")
        
        # Initialize ARAP solver
        # Parameters: vertices, faces, dimension (2 for UV), fixed_vertices
        arap = igl.ARAP(self.vertices, self.faces, 2, np.zeros(0))
        
        # Solve ARAP optimization
        # Parameters: fixed_positions (empty), initial_guess (harmonic UV)
        optimized_uv = arap.solve(np.zeros((0, 0)), self.uv_coords)
        
        if optimized_uv is None or len(optimized_uv) == 0:
            raise RuntimeError("ARAP optimization failed")
        
        self.uv_coords = optimized_uv
        self.log("ARAP optimization completed successfully")
        
        return self.uv_coords
    
    def save_mesh_with_uv(self, output_path):
        """Save mesh with optimized UV coordinates to OBJ file"""
        if self.vertices is None or self.faces is None or self.uv_coords is None:
            raise ValueError("Mesh data or UV coordinates not available")
        
        self.log(f"Saving mesh with UV coordinates to: {output_path}")
        
        # Ensure output directory exists
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Write OBJ file with UV coordinates
        with open(output_path, 'w') as f:
            # Write vertices
            for v in self.vertices:
                f.write(f"v {v[0]:.6f} {v[1]:.6f} {v[2]:.6f}\n")
            
            # Write texture coordinates
            for uv in self.uv_coords:
                f.write(f"vt {uv[0]:.6f} {uv[1]:.6f}\n")
            
            # Write faces with texture coordinate indices
            for face in self.faces:
                # OBJ format uses 1-based indexing
                v1, v2, v3 = face[0] + 1, face[1] + 1, face[2] + 1
                f.write(f"f {v1}/{v1} {v2}/{v2} {v3}/{v3}\n")
        
        self.log("Mesh saved successfully")
    
    def get_uv_statistics(self):
        """Get statistics about UV coordinates"""
        if self.uv_coords is None:
            return None
        
        uv_min = np.min(self.uv_coords, axis=0)
        uv_max = np.max(self.uv_coords, axis=0)
        uv_mean = np.mean(self.uv_coords, axis=0)
        uv_std = np.std(self.uv_coords, axis=0)
        
        return {
            'min': uv_min,
            'max': uv_max,
            'mean': uv_mean,
            'std': uv_std,
            'range': uv_max - uv_min
        }


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Optimize UV coordinates using ARAP method with libigl",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python arap_uv_optimizer.py input.obj output.obj
    python arap_uv_optimizer.py input.obj output.obj --iterations 20
    python arap_uv_optimizer.py input.obj output.obj --quiet
        """
    )
    
    parser.add_argument("input", help="Input OBJ file path")
    parser.add_argument("output", help="Output OBJ file path")
    parser.add_argument("--iterations", type=int, default=10, 
                       help="Maximum number of ARAP iterations (default: 10)")
    parser.add_argument("--quiet", action="store_true", 
                       help="Suppress verbose output")
    
    args = parser.parse_args()
    
    try:
        # Create optimizer
        optimizer = ARAPUVOptimizer(verbose=not args.quiet)
        
        # Load mesh
        optimizer.load_mesh(args.input)
        
        # Compute initial UV parametrization
        optimizer.compute_initial_uv()
        
        # Optimize with ARAP
        optimizer.optimize_with_arap(max_iterations=args.iterations)
        
        # Print UV statistics
        if not args.quiet:
            stats = optimizer.get_uv_statistics()
            if stats:
                print("\n=== UV Coordinate Statistics ===")
                print(f"Range: U=[{stats['min'][0]:.4f}, {stats['max'][0]:.4f}], "
                      f"V=[{stats['min'][1]:.4f}, {stats['max'][1]:.4f}]")
                print(f"Mean: U={stats['mean'][0]:.4f}, V={stats['mean'][1]:.4f}")
                print(f"Std: U={stats['std'][0]:.4f}, V={stats['std'][1]:.4f}")
                print("================================\n")
        
        # Save result
        optimizer.save_mesh_with_uv(args.output)
        
        if not args.quiet:
            print(f"ARAP UV optimization completed successfully!")
            print(f"Result saved to: {args.output}")
    
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        if not args.quiet:
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
